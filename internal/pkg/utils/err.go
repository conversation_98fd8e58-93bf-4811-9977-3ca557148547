package utils

import (
	"errors"
	"github.com/gin-gonic/gin"
	"net/http"
	"strings"
)

// AppError 自定义错误类型
type AppError struct {
	Code    codeType `json:"code"`
	Message string   `json:"message"`
}

func (e *AppError) Error() string {
	return e.Message
}

type codeType int

const (
	ErrInvalidParamsCode      codeType = 400
	ErrUnauthorizedCode       codeType = 401
	ErrForbiddenCode          codeType = 403
	ErrNotFoundCode           codeType = 404
	ErrInternalCode           codeType = 500
	ErrUserNotFoundCode       codeType = 1001
	ErrUserDisabledCode       codeType = 1002
	ErrPasswordWrongCode      codeType = 1003
	ErrUsernameExistCode      codeType = 1004
	ErrFileNotFoundCode       codeType = 2001
	ErrFileTooLargeCode       codeType = 2002
	ErrFileTypeNotAllowedCode codeType = 2003
	ErrFileUploadFailedCode   codeType = 2004
	ErrTaskReportIDExistCode  codeType = 3001
)

// 错误码到默认消息的映射
var defaultMessages = map[codeType]string{
	ErrInvalidParamsCode:      "参数错误",
	ErrUnauthorizedCode:       "未授权",
	ErrForbiddenCode:          "禁止访问",
	ErrNotFoundCode:           "资源不存在",
	ErrInternalCode:           "服务器内部错误",
	ErrUserNotFoundCode:       "用户不存在",
	ErrUserDisabledCode:       "用户已被禁用",
	ErrPasswordWrongCode:      "用户名或密码错误",
	ErrUsernameExistCode:      "用户名已存在",
	ErrFileNotFoundCode:       "文件不存在",
	ErrFileTooLargeCode:       "文件过大",
	ErrFileTypeNotAllowedCode: "文件类型不允许",
	ErrFileUploadFailedCode:   "文件上传失败",
	ErrTaskReportIDExistCode:  "报告ID已存在",
}

// 预定义错误
var (
	ErrInvalidParams      = &AppError{Code: ErrInvalidParamsCode, Message: "参数错误"}
	ErrUnauthorized       = &AppError{Code: ErrUnauthorizedCode, Message: "未授权"}
	ErrForbidden          = &AppError{Code: ErrForbiddenCode, Message: "禁止访问"}
	ErrNotFound           = &AppError{Code: ErrNotFoundCode, Message: "资源不存在"}
	ErrInternalServer     = &AppError{Code: ErrInternalCode, Message: "服务器内部错误"}
	ErrUserNotFound       = &AppError{Code: ErrUserNotFoundCode, Message: "用户不存在"}
	ErrUserDisabled       = &AppError{Code: ErrUserDisabledCode, Message: "用户已被禁用"}
	ErrPasswordWrong      = &AppError{Code: ErrPasswordWrongCode, Message: "用户名或密码错误"}
	ErrUsernameExist      = &AppError{Code: ErrUsernameExistCode, Message: "用户名已存在"}
	ErrMobileExist        = &AppError{Code: ErrUsernameExistCode, Message: "手机号已存在"}
	ErrFileNotFound       = &AppError{Code: ErrFileNotFoundCode, Message: "文件不存在"}
	ErrFileTooLarge       = &AppError{Code: ErrFileTooLargeCode, Message: "文件过大"}
	ErrFileTypeNotAllowed = &AppError{Code: ErrFileTypeNotAllowedCode, Message: "文件类型不允许"}
	ErrFileUploadFailed   = &AppError{Code: ErrFileUploadFailedCode, Message: "文件上传失败"}
	ErrTaskReportIDExist  = &AppError{Code: ErrTaskReportIDExistCode, Message: "报告任务已存在"}
)

// NewAppError 创建自定义错误
// message 参数为可选，如果不提供或为空则使用默认消息
func NewAppError(code codeType, message string) *AppError {
	msg := strings.TrimSpace(message)
	if msg == "" {
		// 如果没有提供消息或消息为空，使用默认消息
		if defaultMsg, exists := defaultMessages[code]; exists {
			msg = defaultMsg
		} else {
			msg = "未知错误"
		}
	}
	return &AppError{
		Code:    code,
		Message: msg,
	}
}

// ResponseError 统一错误处理
func ResponseError(ctx *gin.Context, err error) {
	var appErr *AppError
	if errors.As(err, &appErr) {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    appErr.Code,
			"message": appErr.Message,
		})
	} else {
		ctx.JSON(http.StatusOK, gin.H{
			"code":    500,
			"message": "服务器内部错误",
		})
	}
}
